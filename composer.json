{"name": "hyperf/hyperf-skeleton", "type": "project", "keywords": ["php", "swoole", "framework", "hyperf", "microservice", "middleware"], "description": "A coroutine framework that focuses on hyperspeed and flexible, specifically use for build microservices and middlewares.", "license": "Apache-2.0", "require": {"php": ">=8.1", "96qbhy/hyperf-auth": "^3.1", "carbonphp/carbon-doctrine-types": "^2.1", "doctrine/dbal": "^3.0", "hyperf/async-queue": "^3.1", "hyperf/cache": "~3.1.0", "hyperf/command": "~3.1.0", "hyperf/config": "^3.1", "hyperf/constants": "^3.1@RC", "hyperf/database": "~3.1.0", "hyperf/db-connection": "~3.1.0", "hyperf/engine": "^2.10", "hyperf/framework": "~3.1.0", "hyperf/guzzle": "~3.1.0", "hyperf/helper": "^3.1", "hyperf/http-server": "~3.1.0", "hyperf/logger": "~3.1.0", "hyperf/memory": "~3.1.0", "hyperf/process": "^3.1", "hyperf/redis": "~3.1.0", "hyperf/session": "^3.1", "hyperf/signal": "^3.1", "hyperf/validation": "^3.1", "hyperf/websocket-server": "^3.1", "lustre/php-dfa-sensitive": "^1.4", "ramsey/uuid": "^4.9"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "hyperf/devtool": "~3.1.0", "hyperf/testing": "~3.1.0", "mockery/mockery": "^1.0", "phpstan/phpstan": "^1.0", "swoole/ide-helper": "^5.0"}, "suggest": {"ext-openssl": "Required to use HTTPS.", "ext-json": "Required to use JSON.", "ext-pdo": "Required to use MySQL Client.", "ext-pdo_mysql": "Required to use MySQL Client.", "ext-redis": "Required to use Redis Client."}, "autoload": {"psr-4": {"App\\": "app/"}, "files": ["app/functions.php"]}, "autoload-dev": {"psr-4": {"HyperfTest\\": "./test/"}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"optimize-autoloader": true, "sort-packages": true}, "extra": [], "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-autoload-dump": ["rm -rf runtime/container"], "test": "co-phpunit --prepend test/bootstrap.php -c phpunit.xml --colors=always", "cs-fix": "php-cs-fixer fix $1", "analyse": "phpstan analyse --memory-limit 300M -l 0 -c phpstan.neon ./app ./config", "start": ["Composer\\Config::disableProcessTimeout", "php ./bin/hyperf.php start"]}}
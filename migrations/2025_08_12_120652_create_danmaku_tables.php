<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateDanmakuTables extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('danmaku', function (Blueprint $table) {
            $table->id('id');
            $table->bigInteger('vid')->nullable(false)->default(0)->comment('游客ID');
            $table->string('content', 1000)->nullable(false)->default('')->comment('弹幕评论显示内容');
            $table->string('real_content', 1000)->nullable(false)->default('')->comment('弹幕评论原始内容');
            $table->datetimes();
            $table->index('vid');
            $table->comment("视频弹幕评论");
        });

        Schema::create('visitors', function (Blueprint $table) {
            $table->id('id');
            $table->bigInteger('uid')->nullable(false)->default(0)->comment('所属 user');
            $table->string('ip')->nullable(false)->default('')->comment('ip');
            $table->string('fingerprint', 500)->nullable(true)->comment('浏览器指纹信息 hash');
            $table->integer('last_fd')->nullable(false)->default(0)->comment('最近一次的消息请求编号');
            $table->datetimes();
            $table->unique(['uid', 'ip', 'fingerprint']);
            $table->comment('游客信息');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('danmaku');
        Schema::dropIfExists('visitors');
    }
}

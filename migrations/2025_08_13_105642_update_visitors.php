<?php

use Doctrine\DBAL\Query;
use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;
use Hyperf\DbConnection\Db;

class UpdateVisitors extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('visitors', function (Blueprint $table) {
            $table->renameColumn('fingerprint', 'fingerprint_hash');
            $table->string('sn')->nullable(false)->default('')->comment('唯一编号')->after('id');
            $table->index('sn');
        });
        DB::table('visitors')->where('sn', '')->update(['sn' => DB::raw('UUID()')]);
        Schema::table('visitors', function (Blueprint $table) {
            $table->text('fingerprint')->nullable(true)->comment('浏览器指纹信息')->after('fingerprint_hash');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('visitors', function (Blueprint $table) {
            $table->dropColumn('fingerprint');
            $table->dropColumn('sn');
            $table->renameColumn('fingerprint_hash', 'fingerprint');
        });
    }
}

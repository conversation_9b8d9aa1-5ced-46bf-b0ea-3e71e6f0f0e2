<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateVisitorLogs extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('visitor_logs', function (Blueprint $table) {
            $table->id('id');
            $table->bigInteger('vid')->nullable(false)->default(0)->comment('游客ID');
            $table->string('event')->nullable(false)->default('')->comment('动作:login/logout');
            $table->text('extras')->nullable(true)->comment('额外信息');
            $table->datetimes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('visitor_logs');
    }
}

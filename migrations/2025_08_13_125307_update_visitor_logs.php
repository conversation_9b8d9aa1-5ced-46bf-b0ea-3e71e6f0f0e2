<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('visitor_logs', function (Blueprint $table) {
            $table->integer('fd')->nullable(false)->default(0)->comment('请求消息编号')->after('vid');
            $table->dateTime('end_at')->nullable(true)->comment('结束于')->after('extras');
            $table->dateTime('start_at')->nullable(true)->comment('开始于')->after('extras');
        });
        Schema::table('visitors', function (Blueprint $table) {
            $table->renameColumn('uid', 'aid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('visitor_logs', function (Blueprint $table) {
            $table->dropColumn('fd');
            $table->dropColumn('start_at');
            $table->dropColumn('end_at');
        });
        Schema::table('visitors', function (Blueprint $table) {
            $table->renameColumn('aid', 'uid');
        });
    }
};

<?php
declare(strict_types=1);
/*
 * 自定义全局函数
 */


/**
 * 阿里直播地址生成
 * @param string $uri 推/拉流地址
 * @param string $key 密钥
 * @param int $exp 过期时间戳
 * @return string
 */
function generateLiveUrl(string $uri, string $key, int $exp): string
{
    $parts = parse_url($uri);

    $scheme = ($parts['scheme'] ?? 'rtmp') . '://';
    $host = $parts['host'] ?? '';
    $path = $parts['path'] ?? '/';
    $query = $parts['query'] ?? '';

    $rand = '0'; // 默认值
    $uid = '0'; // 默认值

    $signStr = sprintf('%s-%u-%s-%s-%s', $path, $exp, $rand, $uid, $key);
    $hash = md5($signStr);
    $authKey = sprintf('%u-%s-%s-%s', $exp, $rand, $uid, $hash);

    // 拼接 query
    parse_str($query, $queryArr);
    $queryArr['auth_key'] = $authKey;
    $finalQuery = http_build_query($queryArr);

    return sprintf('%s%s%s?%s', $scheme, $host, $path, $finalQuery);
}


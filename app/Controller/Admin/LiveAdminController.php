<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Service\LiveService;
use Hyperf\Di\Annotation\Inject;
use Psr\SimpleCache\CacheInterface;
use App\Controller\AbstractController;
use Psr\SimpleCache\InvalidArgumentException;

class LiveAdminController extends AbstractController
{
    #[Inject]
    protected CacheInterface $cache;

    #[Inject]
    protected LiveService $liveService;

    // 获取推流地址

    /**
     * 获取推流地址
     * @throws InvalidArgumentException
     */
    public function getPushUrl()
    {
        if (!($liveUrls = $this->cache->get("live"))) {
            $liveUrls = $this->liveService::refreshLiveUrl();
        }
        return $this->response->success(['push_url' => $liveUrls['push'] ?? null]);
    }
}

<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */


namespace App\Controller\Admin;

use App\Request\AdminRequest;
use App\Service\AdminService;
use Hyperf\Di\Annotation\Inject;
use App\Controller\AbstractController;
use Hyperf\HttpServer\Contract\RequestInterface;

class AdminController extends AbstractController
{
    #[Inject]
    protected AdminService $adminService;
    
    public function register(AdminRequest $request)
    {
        [$user] = $this->adminService->register($request->all());

        return $this->response->success([
            'user' => $user
        ]);

    }

    public function login(RequestInterface $request)
    {
        [$token] = $this->adminService->login($request->input('username'), $request->input('password'));

        return $this->response->success([
            'token' => $token
        ]);
    }
    
    public function agents()
    {
        return $this->adminService->getOnlineAgents();
    }
}

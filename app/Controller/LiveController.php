<?php
declare(strict_types=1);

namespace App\Controller;

use App\Service\LiveService;
use Hyperf\Di\Annotation\Inject;
use Psr\SimpleCache\CacheInterface;
use Psr\SimpleCache\InvalidArgumentException;

class LiveController extends AbstractController
{
    #[Inject]
    protected CacheInterface $cache;

    #[Inject]
    protected LiveService $liveService;

    /**
     * 获取拉流地址
     * @throws InvalidArgumentException
     */
    public function getPullUrl()
    {
        if (!($liveUrls = $this->cache->get("live"))) {
            $liveUrls = $this->liveService::refreshLiveUrl();
        }
        return $this->response->success(['pull_url' => $liveUrls['pull'] ?? null]);
    }
}
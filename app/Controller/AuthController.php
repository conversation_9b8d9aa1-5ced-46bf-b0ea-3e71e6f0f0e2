<?php

declare(strict_types=1);

namespace App\Controller;

use App\Model\Agent;
use App\Request\AgentRequest;
use App\Exception\BusinessException;
use App\Service\AgentService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Contract\RequestInterface;

class AuthController extends AbstractController
{
    #[Inject]
    protected AgentService $agentService;

    public function login(RequestInterface $request)
    {
        [$agent, $token] = $this->agentService->login($request->input('code'));

        return $this->response->success([
            'client' => [
                'code' => $agent['code'],
                'ip' => $request->header('x-real-ip'),
                'user_agent' => $request->header('user-agent'),
                'login_time' => time()
            ],
            'token' => $token
        ]);
    }

    public function register(AgentRequest $request)
    {
        [$agent] = $this->agentService->register($request->all());

        return $this->response->success([
            'agent' => $agent
        ]);

    }

    public function user()
    {
        return $this->agentService->user();
    }
}

<?php

namespace App\Controller\Websocket;

use Hyperf\Di\Annotation\Inject;
use Hyperf\Contract\OnCloseInterface;
use Hyperf\Contract\OnMessageInterface;
use Hyperf\Contract\OnOpenInterface;
use Hyperf\Engine\WebSocket\Frame;
use Hyperf\Engine\WebSocket\Opcode;
use Hyperf\Engine\WebSocket\Response;
use Qbhy\HyperfAuth\AuthManager;

abstract class AbstractWSController implements OnMessageInterface, OnOpenInterface, OnCloseInterface
{
    #[Inject]
    protected AuthManager $auth;

    abstract function onMessage($server, $frame): void;

    function onClose($server, int $fd, int $reactorId): void
    {
        // do something
    }

    // ws 连接成功后
    public function onOpen($server, $request): void
    {
        $response = (new Response($server))->init($request);
        $response->push(new Frame(payloadData: 'Opened'));
    }


    // 心跳消息处理
    protected function handlePing($server, $frame): void
    {
        if (trim($frame->data) == 'ping') {
            $server->push($frame->fd, 'fd: ' . $frame->fd . ' Recv: ' . $frame->data);
            return;
        }

        if ($frame->data == Opcode::PING) {
            $server->push(new Frame(Opcode::PONG));
            return;
        }
    }
}
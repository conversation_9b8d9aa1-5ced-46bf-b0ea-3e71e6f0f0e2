<?php
declare(strict_types=1);

namespace App\Controller\Websocket;

use App\Service\AdminService;
use App\Service\AgentService;
use App\Service\DanmakuService;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\SimpleCache\CacheInterface;


class WebSocketController extends AbstractWSController
{
    #[Inject]
    protected AdminService $adminService;

    #[Inject]
    protected AgentService $agentService;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected DanmakuService $service;

    private CacheInterface $cache;

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __construct(ContainerInterface $container)
    {
        $this->cache = $container->get(CacheInterface::class);
    }

    public function onMessage($server, $frame): void
    {
        // 心跳
        $this->handlePing($server, $frame);

        // 管理员
        if ($frame->data == 'admin') {
            $this->adminService->doLogin($frame->fd);
            return;
        }

        // 登录
        if ($frame->data) {
            $decoded = json_decode($frame->data, true);
            if (is_array($decoded) && array_key_exists('login', $decoded)) {
                $data = $decoded['login'];
                $data['fd'] = $frame->fd;
                $this->agentService->doLogin($server, $frame->fd, json_encode(['login' => $data]));
                return;
            }
        }

        // 其他情况
        $server->push($frame->fd, 'fd: ' . $frame->fd . ' Recv: ' . $frame->data);
    }

    public function onOpen($server, $request): void
    {
        try {
            parent::onOpen($server, $request);
        } finally {
            // 登录日志
            [$vid, ,] = $this->cache->get("{$request->fd}_vid", [null, null, null]);
            $this->service->handleVisitorLog($vid, $request->fd);
        }
    }

    public function onClose($server, int $fd, int $reactorId): void
    {
        try {
            if ($this->redis->hExists('admin_fd', (string)$fd)) {
                // 管路员登出
                $this->adminService->doLogout($fd);
            } else {
                // 普通用户登出
                $this->agentService->doLogout($server, $fd);
            }
        } finally {
            // 记录登录日志
            [$vid, ,] = $this->cache->get("{$fd}_vid", [null, null, null]);
            $this->service->handleVisitorLog($vid, $fd);
        }
    }
}

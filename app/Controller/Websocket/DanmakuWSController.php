<?php

declare(strict_types=1);

namespace App\Controller\Websocket;

use Carbon\Carbon;
use App\Model\Visitor;
use Hyperf\Codec\Json;
use DfaFilter\SensitiveHelper;
use App\Service\DanmakuService;
use Psr\SimpleCache\CacheInterface;
use Psr\Container\ContainerInterface;
use DfaFilter\Exceptions\PdsSystemException;
use Psr\Container\NotFoundExceptionInterface;
use Psr\SimpleCache\InvalidArgumentException;
use DfaFilter\Exceptions\PdsBusinessException;
use Psr\Container\ContainerExceptionInterface;
use Hyperf\WebSocketServer\Exception\WebSocketMessageException;

class <PERSON><PERSON>kuWSController extends AbstractWSController
{
    protected DanmakuService $service;
    private CacheInterface $cache;
    private SensitiveHelper $sensitiveHandle;

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws PdsBusinessException
     */
    public function __construct(ContainerInterface $container, DanmakuService $service)
    {
        // 缓存 FadeCase
        $this->cache = $container->get(CacheInterface::class);
        $this->service = $service;
        // 敏感词过滤器
        $this->sensitiveHandle = SensitiveHelper::init()->setTreeByFile(BASE_PATH . '/files/敏感词.txt');
    }

    // 判断是否为 json 字符串
    protected function isJson(string $str): bool
    {
        if (!empty($str)) {
            json_decode($str);
            return json_last_error() === JSON_ERROR_NONE;
        }
        return false;
    }

    /**
     * @throws InvalidArgumentException
     * @throws PdsBusinessException
     * @throws PdsSystemException
     */
    public function onMessage($server, $frame): void
    {
        // 心跳
        $this->handlePing($server, $frame);

        // 访客信息
        [$vid, $vsn, $aid] = $this->cache->get("{$frame->fd}_vid", [null, null, null]);
        if (!$vid || !(Visitor::where('id', $vid)->exists())) {
            throw new WebSocketMessageException("找不到用户信息");
        }

        // 用户消息体
        $data = $this->isJson($frame->data) ? Json::decode($frame->data) : $frame->data;

        if (!is_array($data) || !isset($data['content'])) {
            $server->push($frame->fd, 'fd: ' . $frame->fd . ' Recv: ' . $frame->data);
            return;
        }

        $originContent = trim($data['content']) ?: '';
        if ($originContent === '') {
            throw new WebSocketMessageException("消息不能为空");
        }

        // 敏感词过滤
        $content = $this->sensitiveHandle->replace($originContent, '*', true);
        $at = Carbon::now()->toDateTimeString();
        $respData = compact('vid', 'vsn', 'content', 'at');

        // 异步存库
        $this->service->handleDanmakuMsg($vid, $content, $originContent);

        // 给自己推送
        $server->push($frame->fd, Json::encode($respData + ['is_self' => true]));

        $this->broadcastToSameUser($server, $frame->fd, $aid, $respData);
    }

    // 消息发送给同 User 下的所有用户（除自己）
    protected function broadcastToSameUser($server, int $currentFd, int $agentId, array $respData): void
    {
        foreach ($server->connections as $fd) {

            [, , $otherAgentId] = $this->cache->get("{$fd}_vid", [null, null, null]);

            if (
                !$server->isEstablished($fd)
                || $fd == $currentFd
            ) {
                // 如果不在线，或者给自己推送了，或者不属于同一个用户，就跳过
                continue;
            }

            $server->push($fd, Json::encode($respData + ['is_self' => false]));
        }
    }
}

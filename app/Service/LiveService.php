<?php

declare(strict_types=1);

namespace App\Service;

use Hyperf\Cache\Annotation\CachePut;

class LiveService
{

    // 刷新直播推/拉流地址并缓存
    #[CachePut(prefix: "live", ttl: 24 * 3600)]
    static public function refreshLiveUrl(): array
    {
        // 应用和推流名称，用于唯一性保证
        $appName = config('app_name');
        $streamName = config('app_env');
        // 推拉流所需要的参数
        $livePushKey = config('aliyun_live.push_key');
        $livePushUrl = config('aliyun_live.push_url');
        $livePullKey = config('aliyun_live.pull_key');
        $livePullUrl = config('aliyun_live.pull_url');

        // 到期时间
        $exp = time() + 24 * 3600;

        $pushUrl = generateLiveUrl($livePushUrl . '/' . $appName . '/' . $streamName, $livePushKey, $exp);
        $pullUrl = generateLiveUrl($livePullUrl . '/' . $appName . '/' . $streamName, $livePullKey, $exp);

        return [
            'push' => $pushUrl,
            'pull' => $pullUrl,
        ];
    }
}
<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Service;

use App\Constants\ErrorCode;
use App\Exception\BusinessException;
use App\Model\Agent;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;
use Hyperf\Redis\Redis;
use App\Service\MessageService;

class AgentService
{
    #[Inject]
    private AuthManager $auth;

    #[Inject]
    protected Redis $redis;

    #[Inject]
    protected MessageService $messageService;

    public function register(array $params)
    {
        // 入库
        $agent = Agent::query()->create($params);

        return [$agent];
    }

    public function login($code)
    {
        // 校验用户是否存在
        $agent = Agent::query()->where(['code' => $code])->first();
        if (empty($agent)) {
            throw new BusinessException(ErrorCode::SERVER_ERROR, '代理商编码有误！');
        }

        if ($agent->status = 0) {
            throw new BusinessException(ErrorCode::SERVER_ERROR, '代理商编码已过期！');
        }

        $token = $this->auth->login($agent);

        return [$agent, $token];
    }

    public function doLogin($server, $fd, $data): void
    {
        $this->redis->hSet('agent_online', (string)$fd, $data);

        //message to admin
        $this->messageService->toAdmin($server, $data);
    }

    public function doLogout($server, $fd): void
    {
        //var_dump($fd);
        $data = json_decode($this->redis->hGet('agent_online', (string)$fd), true);
        $message = json_encode(['logout' => ['code' => $data['login']['code'], 'browserId' => $data['login']['browserId'], 'fd' => $fd]]);
        //var_dump($message);
        $this->messageService->toAdmin($server, $message);
        $this->redis->hDel('agent_online', (string)$fd);
    }
}
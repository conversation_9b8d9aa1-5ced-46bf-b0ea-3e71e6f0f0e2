<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

namespace App\Service;

use App\Constants\ErrorCode;
use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;
use App\Model\User;
use App\Model\Agent;
use Qbhy\HyperfAuth\AuthManager;

class AdminService
{
    #[Inject]
    protected Redis $redis;

    #[Inject]
    private AuthManager $auth;

    public function register(array $params)
    {
        // 入库
        $user = new User();
        $user->name = $params['name'];
        $user->username = $params['username'];
        $user->password = password_hash($params['password'], PASSWORD_DEFAULT);
        $user->save();

        return [$user];
    }

    public function login($username, $password)
    {
        // 校验用户是否存在
        $user = User::query()->where(['username' => $username])->first();
        if (empty($user)) {
            throw new BusinessException(ErrorCode::SERVER_ERROR, '用户名不存在！');
        }

        if (!password_verify($password, $user['password'])) {
            throw new BusinessException(ErrorCode::SERVER_ERROR, '密码错误！');
        }

        $token = $this->auth->guard('admin')->login($user);

        return [$token];
    }

    public function doLogin($fd): void
    {
        $this->redis->hSet('admin_fd', (string)$fd, (string)time());
    }

    public function doLogout($fd): void
    {
        //var_dump($fd);
        $this->redis->hDel('admin_fd', (string)$fd);
    }

    public function getAgents()
    {
        $online_agents_fd = $this->redis->hGetall('agent_online');
        //var_dump($online_agents_fd);

        $agent_order_list = [];
        $all_agents = Agent::all();
        //var_dump($all_agents);
        if (count($online_agents_fd)) {
            foreach ($online_agents_fd as $fd => $info) {
                $info_array = json_decode($info, true);
                //var_dump($info_array);
                if (array_key_exists($info_array['login']['code'], $agent_order_list)) {
                    if (array_key_exists($info_array['login']['browserId'], $agent_order_list[$info_array['login']['code']]['online_clients'])) {
                        $agent_order_list[$info_array['login']['code']]['online_clients'][$info_array['login']['browserId']]['thread_num'] += 1;
                    } else {
                        $agent_order_list[$info_array['login']['code']]['online_clients'][$info_array['login']['browserId']] = [
                            'ip' => $info_array['login']['ip'],
                            'user_agent' => $info_array['login']['user_agent'],
                            'login_time' => date('Y-m-d H:i:s', $info_array['login']['login_time']),
                            'thread_num' => 1,
                        ];
                    }
                } else {
                    $agent = $all_agents->where('code', $info_array['login']['code'])->first();
                    $agent_order_list[$info_array['login']['code']] = [
                        'company_name' => $agent->company_name,
                        'contact_name' => $agent->contact_name,
                        'contact_phone' => $agent->contact_phone,
                        'status' => $agent->status,
                        'isOnline' => true,
                        'online_clients' => [
                            $info_array['login']['browserId'] => [
                                'ip' => $info_array['login']['ip'],
                                'user_agent' => $info_array['login']['user_agent'],
                                'login_time' => date('Y-m-d H:i:s', $info_array['login']['login_time']),
                                'thread_num' => 1,
                            ],
                        ]
                    ];
                    $all_agents = $all_agents->filter(fn($a) => $a->code !== $info_array['login']['code']);
                }
            }
        }

        foreach ($all_agents as $agent) {
            $agent_order_list[$agent->code] = [
                'company_name' => $agent->company_name,
                'contact_name' => $agent->contact_name,
                'contact_phone' => $agent->contact_phone,
                'status' => $agent->status,
                'isOnline' => false,
            ];
        }

        return $agent_order_list;
    }

    // 查询各企业在线访客
    public function getOnlineAgents()
    {
        return Agent::with([
            'visitors' => function ($query) {
                $query->whereHas('logs', function ($query) {
                    $query->whereNull('end_at');
                })->with([
                    'logs' => function ($query) {
                        $query->whereNull('end_at');
                    }
                ]);
            }
        ])->get()->each(function($agent) {
            $agent->append('isOnline');
        });
    }
}
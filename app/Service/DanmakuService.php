<?php

declare(strict_types=1);

namespace App\Service;

use App\Model\Danmaku;
use App\Model\VisitorLog;
use Carbon\Carbon;
use Hyperf\AsyncQueue\Annotation\AsyncQueueMessage;

class DanmakuService
{
    // 消息队列处理弹幕消息存库等后续操作。传入访客 visitor 主键及消息内容
    #[AsyncQueueMessage(pool: 'default')]
    public function handleDanmakuMsg(int $vid, string $content, string $realContent): void
    {
        Danmaku::create(['vid' => $vid, 'content' => $content, 'real_content' => $realContent]);
    }

    // 访客日志
    #[AsyncQueueMessage(pool: 'default')]
    public function handleVisitorLog(int $vid, int $fd): void
    {
        $condition = [
            'vid' => $vid,
            'fd' => $fd,
            'event' => 'online'
        ];
        $visitorLog = VisitorLog::where($condition)->whereNull('end_at')->orderBy('id', 'desc')->first();

        if (!$visitorLog) {
            // 登录
            VisitorLog::create(
                $condition + [
                    'extras' => null,
                    'start_at' => Carbon::now()
                ]
            );
        } else {
            // 登出
            $visitorLog->update([
                'end_at' => Carbon::now()
            ]);
        }
    }
}

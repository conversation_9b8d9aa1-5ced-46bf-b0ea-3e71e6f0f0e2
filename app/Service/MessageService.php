<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Service;

use App\Constants\ErrorCode;
use App\Exception\BusinessException;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Redis\Redis;


class MessageService
{
    #[Inject]
    protected Redis $redis;

    public function toAdmin($server, $data): void
    {
        $admin_fds = $this->redis->hKeys('admin_fd');

        if(count($admin_fds)) {
            foreach($admin_fds as $admin_fd) {
                $server->push((int)$admin_fd, $data);
            }
        }
        //$server->push($frame->fd, $frame->data);
    }

}
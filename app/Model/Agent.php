<?php

declare(strict_types=1);

namespace App\Model;

use Hyperf\DbConnection\Model\Model;
use Qbhy\HyperfAuth\Authenticatable;

/**
 * @property int $id
 * @property string $code
 * @property string $company_name
 * @property string $telphone
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Agent extends Model implements Authenticatable
{
    /**
     * The table associated with the model.
     */
    protected ?string $table = 'agents';

    /**
     * The attributes that are mass assignable.
     */
    protected array $fillable = ['code', 'company_name', 'contact_name', 'contact_phone'];

    /**
     * The attributes that should be cast to native types.
     */
    protected array $casts = ['id' => 'integer', 'created_at' => 'datetime', 'updated_at' => 'datetime'];

    public static function retrieveById($key): ?Authenticatable
    {
        return self::query()->find($key);
    }

    public function getId()
    {
        return $this->id;
    }

    // 旗下的访客信息
    public function visitors(): \Hyperf\Database\Model\Relations\HasMany
    {
        return $this->hasMany(Visitor::class, 'aid');
    }

    // 旗下是否有人在线的状态
    public function getIsOnlineAttribute()
    {
        return count($this->visitors ?? []) > 0;
    }
}

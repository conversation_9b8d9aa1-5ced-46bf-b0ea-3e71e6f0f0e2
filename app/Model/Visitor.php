<?php

declare(strict_types=1);

namespace App\Model;


/**
 * @property int $id
 * @property string $sn
 * @property int $uid
 * @property string $ip
 * @property string $fingerprint
 * @property string $fingerprint_hash
 * @property int $last_fd
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Visitor extends Model
{
    protected ?string $table = 'visitors';

    public function logs(): \Hyperf\Database\Model\Relations\HasMany
    {
        return $this->hasMany(VisitorLog::class, 'vid');
    }
}

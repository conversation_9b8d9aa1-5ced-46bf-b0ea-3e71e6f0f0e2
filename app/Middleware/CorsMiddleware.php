<?php

declare(strict_types=1);

namespace App\Middleware;

use Hyperf\Config\Annotation\Value;
use Hyperf\Context\Context;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;

class CorsMiddleware implements MiddlewareInterface
{

    #[Value('cors')]
    private array $corsConfig;

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        // 允许通过的域名
        $allowOrigin = $this->corsConfig['allow_origin'];
        $origin = $request->getHeaderLine('origin');

        $response = Context::get(ResponseInterface::class);
        $response = $response->withHeader(
            'Access-Control-Allow-Origin', in_array($origin, $allowOrigin) ? $origin : ''
        )->withHeader(
            'Access-Control-Allow-Credentials', $this->corsConfig['allow_credentials'] ? 'true' : 'false'
        )->withHeader(
            'Access-Control-Allow-Headers', implode(',', $this->corsConfig['allow_headers'])
        )->withHeader(
            'Access-Control-Allow-Methods', implode(',', $this->corsConfig['allow_methods'])
        );

        Context::set(ResponseInterface::class, $response);

        if ($request->getMethod() == 'OPTIONS') {
            return $response;
        }

        return $handler->handle($request);
    }
}

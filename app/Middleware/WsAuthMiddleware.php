<?php
declare(strict_types=1);

namespace App\Middleware;

use App\Model\Visitor;
use Hyperf\Stringable\Str;
use Hyperf\Di\Annotation\Inject;
use Psr\SimpleCache\CacheInterface;
use Qbhy\HyperfAuth\AuthManager;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\SimpleCache\InvalidArgumentException;
use Hyperf\WebSocketServer\Exception\WebSocketHandShakeException;

class WsAuthMiddleware
{
    #[Inject]
    protected AuthManager $auth;

    private mixed $cache;

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function __construct(ContainerInterface $container)
    {
        $this->cache = $container->get(CacheInterface::class);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws InvalidArgumentException
     */
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $queryParams = $request->getQueryParams();
        // 鉴权 Token, 登录接口返回
        $token = $queryParams['token'] ?? null;

        if (!$token) {
            return $this->unauthorized('Unauthorized: no token');
        }

        $guard = $this->auth;

        if (!$guard->check($token)) {
            return $this->unauthorized('Unauthorized: invalid token');
        }

        // 缓存用户信息与 fd
        $this->cacheUserInfo($request, $guard->user($token));

        return $handler->handle($request);
    }

    protected function unauthorized(string $message)
    {
        throw new WebSocketHandShakeException($message);
    }

    // 缓存用户信息

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws InvalidArgumentException
     */
    protected function cacheUserInfo(ServerRequestInterface $request, $agent): void
    {
        // 获取IP
        $ip = $request->getServerParams()['remote_addr'] ?? 'unknown';
        // 浏览器指纹
        $fingerprint = $request->getQueryParams()['fingerprint'] ?? 'unknown';
        // 指纹hash
        $fingerprintHash = hash('sha256', $fingerprint);

        // ws 请求编号
        $swooleRequest = $request->getSwooleRequest();
        $fd = $swooleRequest->fd;

        // 查新访客信息
        $condition = [
            'aid' => $agent->id,
            'ip' => $ip,
            'fingerprint_hash' => $fingerprintHash,
        ];
        $visitor = Visitor::firstOrCreate($condition, [
            'sn' => Str::uuid()->toString(),
            'last_fd' => $fd,
            'fingerprint' => $fingerprint,
        ]);
        $visitor->last_fd = $fd;
        $visitor->save();

        // 缓存驱动
        $this->cache->set("{$fd}_vid", [$visitor->id, $visitor->sn, $agent->id]);
    }
}

<?php

declare(strict_types=1);

/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */

use App\Controller\AuthController;
use App\Controller\LiveController;
use Hyperf\HttpServer\Router\Router;
use App\Controller\Admin\AdminController;
use App\Controller\Admin\LiveAdminController;

// 接口
Router::post('/register', [AuthController::class, 'register']);
Router::post('/login', [AuthController::class, 'login']);
Router::addGroup(
    '',
    function () {
        Router::addRoute(['GET', 'POST', 'HEAD'], '/user', [AuthController::class, 'user']);
        // 获取直播拉流地址
        Router::get('/live/pull-url', [LiveController::class, 'getPullUrl']);
    },
    [
        'middleware' => [Qbhy\HyperfAuth\AuthMiddleware::class]
    ]
);

// WebSocket 服务
Router::addServer('ws', function () {
    Router::get('/', 'App\Controller\Websocket\WebSocketController');
    Router::get('/danmaku', 'App\Controller\Websocket\DanmakuWSController');
});

// 管理端
Router::post('/admin/register', [AdminController::class, 'register']);
Router::post('/admin/login', [AdminController::class, 'login']);
Router::addGroup(
    '/admin',
    function () {
        Router::addRoute(['GET', 'POST', 'HEAD'], '/all-agents', [AdminController::class, 'agents']);
        // 获取直播推流地址
        Router::get('/live/push-url', [LiveAdminController::class, 'getPushUrl']);
    },
    [
        'middleware' => [Qbhy\HyperfAuth\AuthMiddleware::class]
    ]
);
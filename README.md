

## 环境要求
需要确保您的运行环境达到了以下要求：

- PHP >= 8.1
- 以下任意一种网络引擎：
   - Swoole PHP扩展 >= 5.0，并在`php.ini`中设置`swoole.use_shortname='Off'`
   - Swow PHP扩展 >= 1.3
- JSON PHP扩展
- Pcntl PHP扩展
- OpenSSL PHP扩展（如果需要使用HTTPS）
- PDO PHP扩展（如果需要使用MySQL客户端）
- Redis PHP扩展（如果需要使用Redis客户端）
- Protobuf PHP扩展（如果需要使用gRPC服务端或客户端）


## 快速开始

安装完成后，您可以使用以下命令立即启动服务器。

```bash
cd 安装路径
php bin/hyperf.php start
```

## 数据库迁移
```bash
# 创建迁移
php bin/hyperf.php gen:migration create_users_table

# 执行
php bin/hyperf.php migrate

# 回滚
php bin/hyperf.php migrate:rollback
# 分阶段回滚
php bin/hyperf.php migrate:rollback --step=5
```

## 消息队列


## 提示

- 一个实用的建议是将`hyperf-skeleton`重命名为您实际的项目名称，包括`composer.json`和`docker-compose.yml`等文件。
- 查看`config/routes.php`和`app/Controller/IndexController.php`可以了解HTTP入口点的示例。

